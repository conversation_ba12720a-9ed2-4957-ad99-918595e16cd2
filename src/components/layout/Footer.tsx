import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../context/LanguageContext';
import { useContactSettings, useSocialMediaLinks, useContactInfo } from '../../hooks/useContactSettings';

const Footer: React.FC = () => {
  const { translations } = useLanguage();
  const { settings, loading } = useContactSettings();
  const socialLinks = useSocialMediaLinks();
  const contactInfo = useContactInfo();

  return (
    <footer className="bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto pt-16 pb-8 px-4 sm:px-6 lg:px-8">
        {/* Logo and description */}
        <div className="flex flex-col md:flex-row justify-between items-start mb-12">
          <div className="mb-8 md:mb-0 md:max-w-lg">
            {/* Enhanced Logo Section */}
            <div className="relative">
              <Link to="/" className="inline-block group">
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white via-blue-50 to-indigo-100 p-6 shadow-xl group-hover:shadow-2xl transition-all duration-500 border border-blue-200/50">
                  {/* Animated background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* Logo and brand */}
                  <div className="relative flex items-center space-x-4">
                    <div className="relative">
                      <img
                        src="/assets/images/MaBoursedetudeLogo.jpeg"
                        alt="MaBourse Logo"
                        className="h-16 w-auto rounded-xl shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // Create a text logo as fallback
                          const textLogo = document.createElement('div');
                          textLogo.className = 'h-16 flex items-center px-6 bg-gradient-to-r from-primary to-primary-dark text-white font-bold text-2xl rounded-xl shadow-md';
                          textLogo.textContent = 'MaBourse';
                          target.parentNode?.insertBefore(textLogo, target);
                        }}
                      />
                      {/* Glow effect */}
                      <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>

                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300">
                        MaBourse
                      </h3>
                      <div className="flex items-center mt-1 space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                        </div>
                        <span className="text-sm font-medium text-blue-600">Plateforme Éducative</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>

            {/* Enhanced Description */}
            <div className="mt-6 relative">
              <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-6 border border-slate-200/60 shadow-lg">
                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-10">
                  <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
                  </svg>
                </div>

                <p className="text-gray-700 text-base leading-relaxed font-medium mb-4">
                  {!loading && settings?.business_description
                    ? settings.business_description
                    : "Votre plateforme de référence pour trouver des bourses d'études adaptées à votre parcours académique et à vos ambitions professionnelles."
                  }
                </p>

                {/* Values showcase */}
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center bg-white/80 rounded-full px-3 py-1.5 shadow-sm border border-blue-100">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-2"></div>
                    <span className="text-sm font-semibold text-blue-700">Excellence</span>
                  </div>
                  <div className="flex items-center bg-white/80 rounded-full px-3 py-1.5 shadow-sm border border-indigo-100">
                    <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full mr-2"></div>
                    <span className="text-sm font-semibold text-indigo-700">Innovation</span>
                  </div>
                  <div className="flex items-center bg-white/80 rounded-full px-3 py-1.5 shadow-sm border border-purple-100">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full mr-2"></div>
                    <span className="text-sm font-semibold text-purple-700">Réussite</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 w-full md:w-auto">
            {/* Quick Links */}
            <div className="space-y-3">
              <h3 className="text-sm font-bold text-primary tracking-wider uppercase relative inline-block">
                Liens Rapides
                <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-primary-light"></span>
              </h3>
              <ul className="mt-4 space-y-3">
                <li>
                  <Link to="/about" className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center">
                    <svg className="w-3 h-3 mr-2 text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    À Propos
                  </Link>
                </li>
                <li>
                  <Link to="/scholarships" className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center">
                    <svg className="w-3 h-3 mr-2 text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    Bourses
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center">
                    <svg className="w-3 h-3 mr-2 text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    Contact
                  </Link>
                </li>
                <li>
                  <Link to="/scholarships?level=Licence" className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center">
                    <svg className="w-3 h-3 mr-2 text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    Bourses Licence
                  </Link>
                </li>
                <li>
                  <Link to="/scholarships?level=Master" className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center">
                    <svg className="w-3 h-3 mr-2 text-primary-light" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    Bourses Master
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <h3 className="text-sm font-bold text-primary tracking-wider uppercase relative inline-block">
                Contactez-Nous
                <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-primary-light"></span>
              </h3>
              <ul className="mt-4 space-y-3">
                {!loading && contactInfo.emails.map((email, index) => (
                  <li key={`email-${index}`} className="text-sm text-gray-600 flex items-start">
                    <svg className="w-5 h-5 mr-2 text-primary-light mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v10a2 2 0 002 2z" />
                    </svg>
                    <span>{email.value}</span>
                  </li>
                ))}
                {!loading && contactInfo.phones.slice(0, 2).map((phone, index) => (
                  <li key={`phone-${index}`} className="text-sm text-gray-600 flex items-start">
                    <svg className="w-5 h-5 mr-2 text-primary-light mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <span>{phone.value}</span>
                  </li>
                ))}
                {!loading && contactInfo.addresses.map((address, index) => (
                  <li key={`address-${index}`} className="text-sm text-gray-600 flex items-start">
                    <svg className="w-5 h-5 mr-2 text-primary-light mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span dangerouslySetInnerHTML={{ __html: address.value.replace(/\n/g, '<br />') }} />
                  </li>
                ))}
              </ul>
            </div>

            {/* Social Links */}
            <div className="space-y-3">
              <h3 className="text-sm font-bold text-primary tracking-wider uppercase relative inline-block">
                Suivez-Nous
                <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-primary-light"></span>
              </h3>
              <div className="mt-4 flex flex-col space-y-3">
                {!loading && socialLinks.map((social, index) => (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 flex items-center"
                  >
                    <div className="w-8 h-8 rounded-full bg-primary-light/10 flex items-center justify-center mr-2">
                      {social.icon === 'facebook' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                        </svg>
                      )}
                      {social.icon === 'twitter' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                      )}
                      {social.icon === 'linkedin' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path fillRule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clipRule="evenodd" />
                        </svg>
                      )}
                      {social.icon === 'instagram' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                        </svg>
                      )}
                      {social.icon === 'youtube' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                      )}
                      {social.icon === 'telegram' && (
                        <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                        </svg>
                      )}
                    </div>
                    <span>{social.name}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section with copyright and additional links */}
        <div className="border-t border-gray-200 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-500 mb-4 md:mb-0">
            © {new Date().getFullYear()} {translations.brand.name}. {translations.footer.copyright || 'Tous droits réservés.'}
          </p>

          <div className="flex space-x-6">
            <Link to="/privacy" className="text-xs text-gray-500 hover:text-primary transition-colors duration-200">
              Politique de Confidentialité
            </Link>
            <Link to="/terms" className="text-xs text-gray-500 hover:text-primary transition-colors duration-200">
              Conditions d'Utilisation
            </Link>
            <Link to="/cookies" className="text-xs text-gray-500 hover:text-primary transition-colors duration-200">
              Politique de Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;